import mongoose from "mongoose";
import {
	getPreviewVideoUrl,
	getSignedUrlForUpload,
	getThumbnailSignedUrls,
} from "../services/awsSpace.js";
import VideoUpload from "../models/VideoUpload.js";
import User from "../models/User.js";
import { addVideoToQueue } from "../services/videoQueue.js";
import {
	USER_FIELD_MAP,
	videoTypeMapping,
	videoTypeStrToNum,
} from "../constants/index.js";
import path from "path";

const getVideoDataByType = (user, videoType) => {
	const videoDataMap = {
		[videoTypeMapping.EarlyLife]: user.earlyLifeData,
		[videoTypeMapping.ProfessionalLife]: user.professionalLifeData,
		[videoTypeMapping.CurrentLife]: user.currentLifeData,
	};

	return videoDataMap[videoType] || null;
};

export const uploadVideo = async (req, res) => {
	try {
		const { videoType, contentType, fileName } = req.body;
		if (!videoType || !Object.keys(videoTypeStrToNum).includes(videoType)) {
			return res.status(400).json({
				message: "Invalid video type.",
			});
		}

		const user = await User.findById(req.user._id);
		if (!user) {
			return res.status(404).json({ message: "User not found." });
		}

		const videoData = getVideoDataByType(user, videoType);
		if (videoData && videoData.videoId) {
			const video = await VideoUpload.findById(videoData.videoId);
			if (!video) {
				return res
					.status(404)
					.json({ message: "Previous video not found." });
			}
			if (
				video.transcriptionStatus === "pending" ||
				video.transcriptionStatus === "processing"
			) {
				return res.status(400).json({
					message: "Video is being processed.",
				});
			}
		}

		const videoId = new mongoose.Types.ObjectId();
		const signedUrl = await getSignedUrlForUpload(
			`SM360/onboarding/${req.user._id}/${videoId}/${fileName}`,
			contentType
		);

		res.status(200).json({
			message: "Signed URL generated successfully.",
			signedUrl,
			videoId,
		});
	} catch (error) {
		console.log(error);
		res.status(500).json({
			message: "Error generating signed URL.",
			error,
		});
	}
};

export const confirmUpload = async (req, res) => {
	try {
		const { videoId, videoType } = req.body;

		const video = await VideoUpload.create({
			_id: videoId,
			createdBy: req.user._id,
			videoType: videoType,
		});

		// Update user with the latest video of the given type
		const user = await User.findById(req.user._id);
		const stepKey = "onboardingLifeData";
		const targetField = USER_FIELD_MAP[videoType]?.[stepKey];

		if (user) {
			if (!user[targetField]) {
				user[targetField] = {};
			}
			user[targetField].videoId = videoId;
			await user.save();
		}

		addVideoToQueue({
			video,
			userId: req.user._id,
		});

		res.status(200).json({
			message: "Video upload confirmed and is being processed.",
			video,
		});
	} catch (error) {
		console.log(error);
		res.status(500).json({
			message: "Error confirming video upload.",
			error,
		});
	}
};

export const getVideoStatus = async (req, res) => {
	try {
		const { videoType, polling = false } = req.query;

		const user = await User.findById(req.user._id);
		if (!user) {
			return res.status(404).json({ message: "User not found." });
		}

		const videoData = getVideoDataByType(user, videoType);
		if (!videoData?.videoId) {
			return res.status(200).json({
				videoUrl: null,
				transcriptionStatus: "failed",
			});
		}

		const video = await VideoUpload.findById(videoData.videoId);

		if (!video || video.transcriptionStatus === "failed") {
			return res.status(200).json({
				videoUrl: null,
				transcriptionStatus: "failed",
			});
		}

		if (polling) {
			return res.status(200).json({
				transcriptionStatus: video.transcriptionStatus,
			});
		}

		const videoUrl =
			video.transcriptionStatus !== "failed"
				? await getPreviewVideoUrl(req.user._id, videoData.videoId)
				: null;

		res.status(200).json({
			videoUrl,
			transcriptionStatus: video.transcriptionStatus,
		});
	} catch (error) {
		console.error("Error in getVideoStatus:", error);
		res.status(500).json({
			message: "Error fetching video status.",
			error: process.env.NODE_ENV === "development" ? error : {},
		});
	}
};

export const getVideoThumbnails = async (req, res) => {
	try {
		const { userId, videoType } = req.query;

		if (!userId) {
			return res.status(400).json({ message: "User ID is required." });
		}

		if (!videoType) {
			return res.status(400).json({ message: "Video type is required." });
		}

		const user = await User.findById(userId);
		if (!user) {
			return res.status(404).json({ message: "User not found." });
		}

		const videoData = getVideoDataByType(user, videoType);
		if (!videoData || !videoData.videoId) {
			return res.status(404).json({ message: "Video not found." });
		}

		const video = await VideoUpload.findById(videoData.videoId);
		if (!video) {
			return res.status(404).json({ message: "Video record not found." });
		}

		// Get thumbnail URLs (both generated and custom)
		let thumbnailUrls = [];
		let allThumbnailKeys = [];

		// Combine generated and custom thumbnail keys
		if (video.thumbnailKeys && video.thumbnailKeys.length > 0) {
			allThumbnailKeys = [...video.thumbnailKeys];
		}
		if (video.customThumbnailKeys && video.customThumbnailKeys.length > 0) {
			allThumbnailKeys = [
				...allThumbnailKeys,
				...video.customThumbnailKeys,
			];
		}

		if (allThumbnailKeys.length > 0) {
			try {
				thumbnailUrls = await getThumbnailSignedUrls(allThumbnailKeys);
			} catch (error) {
				console.error("Error generating thumbnail URLs:", error);
			}
		}

		// Get selected thumbnail URL if available
		let selectedThumbnailUrl = null;
		if (video.selectedThumbnailKey) {
			try {
				const selectedUrls = await getThumbnailSignedUrls([
					video.selectedThumbnailKey,
				]);
				selectedThumbnailUrl = selectedUrls[0] || null;
			} catch (error) {
				console.error(
					"Error generating selected thumbnail URL:",
					error
				);
			}
		}

		res.status(200).json({
			thumbnailUrls,
			thumbnailKeys: allThumbnailKeys,
			generatedThumbnailKeys: video.thumbnailKeys || [],
			customThumbnailKeys: video.customThumbnailKeys || [],
			thumbnailStatus: video.thumbnailStatus,
			thumbnailCount: allThumbnailKeys.length,
			selectedThumbnailUrl,
			selectedThumbnailKey: video.selectedThumbnailKey,
		});
	} catch (error) {
		console.error("Error in getVideoThumbnails:", error);
		res.status(500).json({
			message: "Error fetching video thumbnails.",
			error: process.env.NODE_ENV === "development" ? error : {},
		});
	}
};

export const getSelectedThumbail = async (req, res) => {
	try {
		const { userId, videoType } = req.query;

		if (!userId) {
			return res.status(400).json({ message: "User ID is required." });
		}

		if (!videoType) {
			return res.status(400).json({ message: "Video type is required." });
		}

		const user = await User.findById(userId);
		if (!user) {
			return res.status(404).json({ message: "User not found." });
		}

		const videoData = getVideoDataByType(user, videoType);
		if (!videoData || !videoData.videoId) {
			return res.status(404).json({ message: "Video not found." });
		}

		const video = await VideoUpload.findById(videoData.videoId);
		if (!video) {
			return res.status(404).json({ message: "Video record not found." });
		}

		// Get selected thumbnail URL if available
		let selectedThumbnailUrl = null;
		if (video.selectedThumbnailKey) {
			try {
				const selectedUrls = await getThumbnailSignedUrls([
					video.selectedThumbnailKey,
				]);
				selectedThumbnailUrl = selectedUrls[0] || null;
			} catch (error) {
				console.error(
					"Error generating selected thumbnail URL:",
					error
				);
			}
		}

		res.status(200).json({
			selectedThumbnailUrl,
		});
	} catch (error) {
		console.error("Error in getVideoThumbnails:", error);
		res.status(500).json({
			message: "Error fetching video thumbnails.",
			error: process.env.NODE_ENV === "development" ? error : {},
		});
	}
};

export const selectThumbnail = async (req, res) => {
	try {
		const { videoType, thumbnailKey } = req.body;

		if (!videoType || !thumbnailKey) {
			return res.status(400).json({
				message: "Video type and thumbnail key are required.",
			});
		}

		const user = await User.findById(req.user._id);
		if (!user) {
			return res.status(404).json({ message: "User not found." });
		}

		const videoData = getVideoDataByType(user, videoType);
		if (!videoData || !videoData.videoId) {
			return res.status(404).json({ message: "Video not found." });
		}

		const video = await VideoUpload.findById(videoData.videoId);
		if (!video) {
			return res.status(404).json({ message: "Video record not found." });
		}

		// Verify that the thumbnail key exists in either generated or custom thumbnails
		const isGeneratedThumbnail =
			video.thumbnailKeys?.includes(thumbnailKey);
		const isCustomThumbnail =
			video.customThumbnailKeys?.includes(thumbnailKey);

		if (!isGeneratedThumbnail && !isCustomThumbnail) {
			return res.status(400).json({
				message: "Invalid thumbnail key for this video.",
			});
		}

		// Update the selected thumbnail
		await VideoUpload.findByIdAndUpdate(videoData.videoId, {
			selectedThumbnailKey: thumbnailKey,
		});

		// Get the selected thumbnail URL
		const selectedUrls = await getThumbnailSignedUrls([thumbnailKey]);
		const selectedThumbnailUrl = selectedUrls[0] || null;

		res.status(200).json({
			message: "Thumbnail selected successfully.",
			selectedThumbnailKey: thumbnailKey,
			selectedThumbnailUrl,
		});
	} catch (error) {
		console.error("Error in selectThumbnail:", error);
		res.status(500).json({
			message: "Error selecting thumbnail.",
			error: process.env.NODE_ENV === "development" ? error : {},
		});
	}
};

export const getCustomThumbnailSignedUrl = async (req, res) => {
	try {
		const { videoType, fileName, contentType } = req.body;

		if (!videoType || !Object.keys(videoTypeStrToNum).includes(videoType)) {
			return res.status(400).json({
				message: "Invalid video type.",
			});
		}

		if (!fileName || !contentType) {
			return res.status(400).json({
				message: "File name and content type are required.",
			});
		}

		if (!contentType.startsWith("image/")) {
			return res.status(400).json({
				message: "Only image files are allowed.",
			});
		}

		const user = await User.findById(req.user._id);
		if (!user) {
			return res.status(404).json({ message: "User not found." });
		}

		const videoData = getVideoDataByType(user, videoType);
		if (!videoData || !videoData.videoId) {
			return res.status(404).json({ message: "Video not found." });
		}

		// Generate unique key for custom thumbnail
		const timestamp = Date.now();
		const fileExtension = path.extname(fileName);
		const customThumbnailKey = `SM360/onboarding/${req.user._id}/${videoData.videoId}/custom-thumbnails/custom-${timestamp}${fileExtension}`;

		const signedUrl = await getSignedUrlForUpload(
			customThumbnailKey,
			contentType
		);

		res.status(200).json({
			message: "Signed URL generated successfully.",
			signedUrl,
			thumbnailKey: customThumbnailKey,
		});
	} catch (error) {
		console.error("Error in getCustomThumbnailSignedUrl:", error);
		res.status(500).json({
			message: "Error generating signed URL for custom thumbnail.",
			error: process.env.NODE_ENV === "development" ? error : {},
		});
	}
};

export const confirmCustomThumbnailUpload = async (req, res) => {
	try {
		const { videoType, thumbnailKey } = req.body;

		if (!videoType || !Object.keys(videoTypeStrToNum).includes(videoType)) {
			return res.status(400).json({
				message: "Invalid video type.",
			});
		}

		if (!thumbnailKey) {
			return res.status(400).json({
				message: "Thumbnail key is required.",
			});
		}

		const user = await User.findById(req.user._id);
		if (!user) {
			return res.status(404).json({ message: "User not found." });
		}

		const videoData = getVideoDataByType(user, videoType);
		if (!videoData || !videoData.videoId) {
			return res.status(404).json({ message: "Video not found." });
		}

		const video = await VideoUpload.findById(videoData.videoId);
		if (!video) {
			return res.status(404).json({ message: "Video record not found." });
		}

		// Add the custom thumbnail key to the video record
		const updatedVideo = await VideoUpload.findByIdAndUpdate(
			videoData.videoId,
			{
				$addToSet: { customThumbnailKeys: thumbnailKey },
				selectedThumbnailKey: thumbnailKey, // Auto-select the uploaded custom thumbnail
			},
			{ new: true }
		);

		// Get the thumbnail URL
		const thumbnailUrls = await getThumbnailSignedUrls([thumbnailKey]);
		const thumbnailUrl = thumbnailUrls[0] || null;

		res.status(200).json({
			message: "Custom thumbnail uploaded successfully.",
			thumbnailKey,
			thumbnailUrl,
			selectedThumbnailKey: thumbnailKey,
		});
	} catch (error) {
		console.error("Error in confirmCustomThumbnailUpload:", error);
		res.status(500).json({
			message: "Error confirming custom thumbnail upload.",
			error: process.env.NODE_ENV === "development" ? error : {},
		});
	}
};

export const deleteThumbnail = async (req, res) => {
	try {
		const { videoType, thumbnailKey } = req.body;

		if (!videoType || !Object.keys(videoTypeStrToNum).includes(videoType)) {
			return res.status(400).json({
				message: "Invalid video type.",
			});
		}

		if (!thumbnailKey) {
			return res.status(400).json({
				message: "Thumbnail key is required.",
			});
		}

		const user = await User.findById(req.user._id);
		if (!user) {
			return res.status(404).json({ message: "User not found." });
		}

		const videoData = getVideoDataByType(user, videoType);
		if (!videoData || !videoData.videoId) {
			return res.status(404).json({ message: "Video not found." });
		}

		const video = await VideoUpload.findById(videoData.videoId);
		if (!video) {
			return res.status(404).json({ message: "Video record not found." });
		}

		// Check if the thumbnail exists in either thumbnailKeys or customThumbnailKeys
		const isGeneratedThumbnail =
			video.thumbnailKeys?.includes(thumbnailKey);
		const isCustomThumbnail =
			video.customThumbnailKeys?.includes(thumbnailKey);

		if (!isGeneratedThumbnail && !isCustomThumbnail) {
			return res.status(400).json({
				message: "Thumbnail not found for this video.",
			});
		}

		// Mark thumbnail as deleted and remove from active lists
		const updateQuery = {
			$addToSet: { deletedThumbnailKeys: thumbnailKey },
			$pull: {
				thumbnailKeys: thumbnailKey,
				customThumbnailKeys: thumbnailKey,
			},
		};

		// If this was the selected thumbnail, clear the selection
		if (video.selectedThumbnailKey === thumbnailKey) {
			updateQuery.selectedThumbnailKey = null;
		}

		await VideoUpload.findByIdAndUpdate(videoData.videoId, updateQuery);

		res.status(200).json({
			message: "Thumbnail marked as deleted successfully.",
			deletedThumbnailKey: thumbnailKey,
		});
	} catch (error) {
		console.error("Error in deleteThumbnail:", error);
		res.status(500).json({
			message: "Error deleting thumbnail.",
			error: process.env.NODE_ENV === "development" ? error : {},
		});
	}
};
