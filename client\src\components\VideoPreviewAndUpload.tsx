import { Group, Box, Title, Stack, Text, Card, Skeleton } from "@mantine/core";

import { videoTypeLabel } from "../constants";
import type { videoDataType } from "../types";
import VideoThumbnails from "./VideoThumbnails";
import { useCallback, useEffect, useState } from "react";
import apiClient from "../config/axios";

interface VideoPreviewAndUploadProps {
	userId: string | undefined;
	videoPreviewUrl: string | null;
	videoType: videoDataType;
	setHasUnsavedChanges?: (hasUnsavedChanges: boolean) => void;
	editing?: boolean;
	onboardingStepCompleted?: boolean;
}

//remove unused code for upload

const VideoPreviewAndUpload = ({
	userId,
	videoPreviewUrl,
	videoType,
	onboardingStepCompleted,
}: VideoPreviewAndUploadProps) => {
	const [selectedThumbnailUrl, setSelectedThumbnailUrl] = useState<
		string | null
	>(null);
	const [loading, setLoading] = useState(true);

	const fetchSelectedThumbnail = useCallback(async () => {
		try {
			setLoading(true);
			const response = await apiClient.get(
				"/api/videos/thumbnails/select",
				{
					params: { videoType, userId },
				}
			);
			setSelectedThumbnailUrl(response.data.selectedThumbnailUrl);
		} catch (err) {
			console.error("Error fetching selected thumbnail:", err);
		} finally {
			setLoading(false);
		}
	}, [userId, videoType]);

	useEffect(() => {
		if (videoPreviewUrl && onboardingStepCompleted === false) {
			fetchSelectedThumbnail();
		} else {
			setLoading(false);
		}
	}, [
		videoType,
		videoPreviewUrl,
		fetchSelectedThumbnail,
		onboardingStepCompleted,
	]);

	return (
		<Stack gap={"xl"} mb={32}>
			<Group justify="space-between" align="center" wrap="nowrap">
				<Card
					shadow="sm"
					radius="lg"
					withBorder
					h={"28rem"}
					w={"48%"}
					p={"lg"}
				>
					<Stack h="100%" justify="center">
						<Stack gap={1}>
							<Title order={2}>
								{videoTypeLabel[videoType]} Video
							</Title>
							<Text c="gray">Watch the uploaded video.</Text>
						</Stack>
						<Box
							style={{
								backgroundColor: "black",
								borderRadius: "var(--mantine-radius-md)",
								overflow: "hidden",
								height: "70%",
								width: "100%",
							}}
						>
							{loading ? (
								<Skeleton height="100%" radius="lg" />
							) : (
								<video
									controls
									src={videoPreviewUrl ? videoPreviewUrl : ""}
									poster={selectedThumbnailUrl || undefined}
									style={{
										width: "100%",
										height: "100%",
										objectFit: "contain",
									}}
								/>
							)}
						</Box>
					</Stack>
				</Card>
			</Group>

			{onboardingStepCompleted === false && (
				<VideoThumbnails
					userId={userId}
					videoType={videoType}
					onThumbnailSelected={fetchSelectedThumbnail}
				/>
			)}
		</Stack>
	);
};

export default VideoPreviewAndUpload;
