import { useState, useEffect, useCallback, useRef } from "react";
import {
	Box,
	Group,
	Image,
	Text,
	Skeleton,
	Paper,
	ActionIcon,
	Stack,
	ThemeIcon,
	Flex,
	Divider,
	Tooltip,
	FileButton,
} from "@mantine/core";
import { IconUpload, IconX } from "@tabler/icons-react";
import { notifications } from "@mantine/notifications";
import apiClient from "../config/axios";
import type { videoDataType } from "../types";
import { isAxiosError } from "axios";
import axios from "axios";

interface VideoThumbnailsProps {
	videoType: videoDataType;
	userId: string | undefined;
	onThumbnailSelected?: () => void;
}

interface ThumbnailData {
	thumbnailUrls: string[];
	thumbnailKeys: string[];
	generatedThumbnailKeys: string[];
	customThumbnailKeys: string[];
	thumbnailStatus: "pending" | "processing" | "completed" | "failed";
	thumbnailCount: number;
	selectedThumbnailUrl: string | null;
	selectedThumbnailKey: string | null;
}

const VideoThumbnails = ({
	userId,
	videoType,
	onThumbnailSelected,
}: VideoThumbnailsProps) => {
	const fileInputRef = useRef<HTMLInputElement>(null);
	const dragRef = useRef<HTMLDivElement>(null);

	const [thumbnailData, setThumbnailData] = useState<ThumbnailData | null>(
		null
	);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [selecting, setSelecting] = useState(false);
	const [uploading, setUploading] = useState(false);

	const fetchThumbnails = useCallback(async () => {
		try {
			setLoading(true);
			setError(null);

			const response = await apiClient.get("/api/videos/thumbnails", {
				params: { videoType, userId },
			});

			console.log("Fetched thumbnail data:", response.data);
			setThumbnailData(response.data);
		} catch (error) {
			console.error("Error fetching thumbnails:", error);
			let errorMessage = "Failed to load thumbnails";
			if (isAxiosError(error)) {
				errorMessage = error.response?.data?.message || errorMessage;
			} else if (error instanceof Error) {
				errorMessage = error.message || errorMessage;
			}
			setError(errorMessage);
		} finally {
			setLoading(false);
		}
	}, [userId, videoType]);

	const handleThumbnailSelect = async (
		_thumbnailUrl: string,
		index: number
	) => {
		if (!thumbnailData) return;

		try {
			setSelecting(true);

			// Use the actual thumbnail key from the backend
			const thumbnailKey = thumbnailData.thumbnailKeys[index];

			if (!thumbnailKey) {
				throw new Error("Could not determine thumbnail key");
			}

			await apiClient.post("/api/videos/thumbnails/select", {
				videoType,
				thumbnailKey: thumbnailKey,
			});

			// Refresh thumbnail data to get updated selection
			await fetchThumbnails();

			// Call the callback to notify parent component
			onThumbnailSelected?.();

			notifications.show({
				title: "Thumbnail Selected",
				message: "Your thumbnail selection has been saved.",
				color: "green",
			});
		} catch (error) {
			console.error("Error selecting thumbnail:", error);
			let errorMessage = "Failed to select thumbnail";

			if (isAxiosError(error)) {
				errorMessage = error.response?.data?.message || errorMessage;
			} else if (error instanceof Error) {
				errorMessage = error.message || errorMessage;
			}

			notifications.show({
				title: "Selection Failed",
				message: errorMessage,
				color: "red",
			});
		} finally {
			setSelecting(false);
		}
	};

	const handleFileSelect = async (file: File) => {
		if (!file) return;

		if (!file.type.startsWith("image/")) {
			notifications.show({
				title: "Invalid File Type",
				message: "Please select a valid image file",
				color: "red",
				icon: <IconX />,
			});
			return;
		}

		const maxSizeInBytes = 2 * 1024 * 1024;
		if (file.size > maxSizeInBytes) {
			notifications.show({
				title: "File Too Large",
				message: `Please select a Thumbnail image smaller than 2 MB`,
				color: "red",
				icon: <IconX />,
			});
			return;
		}

		// Automatically upload the selected file
		await handleCustomThumbnailUpload(file);
	};

	const handleDrop = (e: React.DragEvent) => {
		e.preventDefault();
		const file = e.dataTransfer.files[0];
		handleFileSelect(file);
	};

	const handleDragOver = (e: React.DragEvent) => {
		e.preventDefault();
	};

	const handleCustomThumbnailUpload = async (file: File | null) => {
		if (!file) return;

		setUploading(true);

		try {
			// Step 1: Get signed URL from backend
			const { data } = await apiClient.post(
				"/api/videos/thumbnails/custom/signed-url",
				{
					videoType,
					fileName: file.name,
					contentType: file.type,
				}
			);

			const { signedUrl, thumbnailKey } = data;

			// Step 2: Upload file directly to AWS using signed URL
			await axios.put(signedUrl, file, {
				headers: {
					"Content-Type": file.type,
				},
			});

			// Step 3: Confirm upload with backend
			const confirmResponse = await apiClient.post("/api/videos/thumbnails/custom/confirm", {
				videoType,
				thumbnailKey,
			});

			console.log("Custom thumbnail upload confirmed:", confirmResponse.data);

			notifications.show({
				title: "Success",
				message: "Custom thumbnail uploaded successfully.",
				color: "green",
			});

			// Refresh thumbnails with a small delay to ensure backend is updated
			setTimeout(async () => {
				await fetchThumbnails();
				onThumbnailSelected?.();
			}, 500);
		} catch (error) {
			console.error("Error uploading custom thumbnail:", error);
			let errorMessage = "Failed to upload custom thumbnail";
			if (isAxiosError(error)) {
				errorMessage = error.response?.data?.message || errorMessage;
			} else if (error instanceof Error) {
				errorMessage = error.message || errorMessage;
			}
			notifications.show({
				title: "Upload Failed",
				message: errorMessage,
				color: "red",
			});
		} finally {
			setUploading(false);
		}
	};

	const handleThumbnailDelete = async (thumbnailKey: string) => {
		try {
			await apiClient.post("/api/videos/thumbnails/delete", {
				videoType,
				thumbnailKey,
			});

			notifications.show({
				title: "Success",
				message: "Thumbnail deleted successfully.",
				color: "green",
			});

			await fetchThumbnails();
			onThumbnailSelected?.();
		} catch (error) {
			console.error("Error deleting thumbnail:", error);
			let errorMessage = "Failed to delete thumbnail";
			if (isAxiosError(error)) {
				errorMessage = error.response?.data?.message || errorMessage;
			} else if (error instanceof Error) {
				errorMessage = error.message || errorMessage;
			}
			notifications.show({
				title: "Delete Failed",
				message: errorMessage,
				color: "red",
			});
		}
	};

	useEffect(() => {
		fetchThumbnails();
	}, [fetchThumbnails, videoType]);

	if (loading) {
		return (
			<Box>
				<Text size="sm" c="dimmed" mb="md">
					Video Thumbnails
				</Text>
				<Group gap="md">
					{Array.from({ length: 3 }).map((_, index) => (
						<Skeleton
							key={index}
							height={120}
							width={160}
							radius="md"
						/>
					))}
				</Group>
			</Box>
		);
	}

	if (error) {
		return (
			<Box>
				<Text size="sm" c="dimmed" mb="xs">
					Video Thumbnails
				</Text>
				<Text size="sm" c="red">
					{error}
				</Text>
			</Box>
		);
	}

	if (!thumbnailData || thumbnailData.thumbnailUrls.length === 0) {
		const statusMessage =
			thumbnailData?.thumbnailStatus === "processing"
				? "Generating thumbnails..."
				: thumbnailData?.thumbnailStatus === "failed"
					? "Failed to generate thumbnails"
					: "No thumbnails available";

		return (
			<Box>
				<Text size="sm" c="dimmed" mb="xs">
					Video Thumbnails
				</Text>
				<Text size="sm" c="dimmed">
					{statusMessage}
				</Text>
			</Box>
		);
	}

	return (
		<Paper
			shadow="md"
			radius="md"
			p="lg"
			withBorder
			style={{
				background: "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)",
			}}
		>
			<Stack gap="md">
				<Divider
					label={
						<Group gap="xs">
							<Text fw={600} size="lg">
								Thumbnail Selection
							</Text>
						</Group>
					}
					labelPosition="left"
				/>
				<Flex gap="sm" wrap={"wrap"}>
					{thumbnailData.thumbnailUrls.map((thumbnailUrl, index) => {
						const isSelected =
							thumbnailData.selectedThumbnailUrl === thumbnailUrl;
						const canClick = !isSelected;
						const thumbnailKey = thumbnailData.thumbnailKeys[index];
						const isCustomThumbnail =
							thumbnailData.customThumbnailKeys?.includes(
								thumbnailKey
							);

						return (
							<>
								<Box
									key={index}
									style={{
										position: "relative",
										minWidth: 160,
									}}
								>
									<Paper
										shadow={isSelected ? "lg" : "sm"}
										radius="md"
										style={{
											cursor: canClick
												? "pointer"
												: "default",
											transition: "all 0.3s ease",
											border: isSelected
												? "3px solid var(--mantine-color-blue-6)"
												: "3px solid transparent",
											opacity: selecting ? 0.7 : 1,
											overflow: "hidden",
											position: "relative",
										}}
										onMouseEnter={e => {
											if (canClick && !selecting) {
												e.currentTarget.style.transform =
													"scale(1.02)";
												e.currentTarget.style.boxShadow =
													"var(--mantine-shadow-md)";
											}
										}}
										onMouseLeave={e => {
											if (canClick && !selecting) {
												e.currentTarget.style.transform =
													"scale(1)";
												e.currentTarget.style.boxShadow =
													isSelected
														? "var(--mantine-shadow-lg)"
														: "var(--mantine-shadow-sm)";
											}
										}}
										onClick={() => {
											if (selecting) return;
											handleThumbnailSelect(
												thumbnailUrl,
												index
											);
										}}
									>
										<Image
											src={thumbnailUrl}
											alt={`Thumbnail ${index + 1}`}
											h={120}
											w={160}
											fit="cover"
											fallbackSrc="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDE2MCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxNjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik03MCA1MEw5MCA2MEw3MCA3MFY1MFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+"
										/>

										{isCustomThumbnail && (
											<Group
												style={{
													position: "absolute",
													top: 8,
													right: 8,
												}}
												gap={4}
											>
												<FileButton
													onChange={file => {
														if (file) {
															handleFileSelect(
																file
															);
														}
													}}
													accept="image/*"
												>
													{props => (
														<Tooltip label="Replace Custom Thumbnail">
															<ActionIcon
																{...props}
																color="blue"
																loading={
																	uploading
																}
															>
																<IconUpload size="1.2rem" />
															</ActionIcon>
														</Tooltip>
													)}
												</FileButton>

												<Tooltip label="Delete Custom Thumbnail">
													<ActionIcon
														color="red"
														onClick={() => {
															handleThumbnailDelete(
																thumbnailKey
															);
														}}
													>
														<IconX size="1.2rem" />
													</ActionIcon>
												</Tooltip>
											</Group>
										)}
									</Paper>
								</Box>
							</>
						);
					})}

					{(!thumbnailData.customThumbnailKeys ||
						thumbnailData.customThumbnailKeys.length === 0) && (
						<Paper
							ref={dragRef}
							onDrop={handleDrop}
							onDragOver={handleDragOver}
							style={{
								border: "2px dashed var(--mantine-color-blue-4)",
								borderRadius: "var(--mantine-radius-md)",
								backgroundColor: "var(--mantine-color-blue-0)",
								textAlign: "center",
								cursor: "pointer",
								transition: "all 0.3s ease",
								height: 120,
								width: 160,
							}}
							onClick={() => fileInputRef.current?.click()}
						>
							<Stack
								gap="sm"
								align="center"
								justify="center"
								h={"100%"}
								w={"100%"}
							>
								<ThemeIcon
									size="lg"
									variant="light"
									color="blue"
								>
									<IconUpload size="1.5rem" />
								</ThemeIcon>
								<Stack gap="xs" align="center">
									<Text size="sm" fw={500} c="blue">
										Upload Custom Thumbnail
									</Text>
									<input
										ref={fileInputRef}
										type="file"
										accept="image/*"
										onChange={e =>
											e.target.files &&
											handleFileSelect(e.target.files[0])
										}
										style={{ display: "none" }}
									/>
								</Stack>
							</Stack>
						</Paper>
					)}
				</Flex>
			</Stack>
		</Paper>
	);
};

export default VideoThumbnails;
